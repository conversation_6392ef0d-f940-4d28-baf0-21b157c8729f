// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	public partial class GcpDb : DbBase
	{
		/// <summary>
		/// 动作库管理
		/// </summary>
		public ITable<LcActionLibrary>        LcActionLibraries       => this.GetTable<LcActionLibrary>();
		/// <summary>
		/// 动作库执行日志
		/// </summary>
		public ITable<LcActionLibraryLog>     LcActionLibraryLogs     => this.GetTable<LcActionLibraryLog>();
		/// <summary>
		/// API定义
		/// </summary>
		public ITable<LcApi>                  LcApis                  => this.GetTable<LcApi>();
		/// <summary>
		/// API网关集群定义
		/// </summary>
		public ITable<LcApiCluster>           LcApiClusters           => this.GetTable<LcApiCluster>();
		/// <summary>
		/// API返回体定义
		/// </summary>
		public ITable<LcApiResponse>          LcApiResponses          => this.GetTable<LcApiResponse>();
		/// <summary>
		/// 第三方用户定义
		/// </summary>
		public ITable<LcAppUser>              LcAppUsers              => this.GetTable<LcAppUser>();
		/// <summary>
		/// AI对话会话
		/// </summary>
		public ITable<LcChatConversation>     LcChatConversations     => this.GetTable<LcChatConversation>();
		/// <summary>
		/// AI对话消息
		/// </summary>
		public ITable<LcChatMessage>          LcChatMessages          => this.GetTable<LcChatMessage>();
		/// <summary>
		/// AI模型配置
		/// </summary>
		public ITable<LcChatModelConfig>      LcChatModelConfigs      => this.GetTable<LcChatModelConfig>();
		/// <summary>
		/// AI对话配置文件
		/// </summary>
		public ITable<LcChatProfile>          LcChatProfiles          => this.GetTable<LcChatProfile>();
		/// <summary>
		/// 数据字典
		/// </summary>
		public ITable<LcDataDict>             LcDataDicts             => this.GetTable<LcDataDict>();
		/// <summary>
		/// 数据源
		/// </summary>
		public ITable<LcDataSource>           LcDataSources           => this.GetTable<LcDataSource>();
		/// <summary>
		/// 数据源属性
		/// </summary>
		public ITable<LcDataSourceAttr>       LcDataSourceAttrs       => this.GetTable<LcDataSourceAttr>();
		/// <summary>
		/// 目录树
		/// </summary>
		public ITable<LcDirTree>              LcDirTrees              => this.GetTable<LcDirTree>();
		/// <summary>
		/// 函数池
		/// </summary>
		public ITable<LcFunction>             LcFunctions             => this.GetTable<LcFunction>();
		/// <summary>
		/// 函数代码属性
		/// </summary>
		public ITable<LcFunctionAttr>         LcFunctionAttrs         => this.GetTable<LcFunctionAttr>();
		/// <summary>
		/// 函数代码
		/// </summary>
		public ITable<LcFunctionCode>         LcFunctionCodes         => this.GetTable<LcFunctionCode>();
		/// <summary>
		/// 函数代码版本日志
		/// </summary>
		public ITable<LcFunctionCodeVersionLog> LcFunctionCodeVersionLogs => this.GetTable<LcFunctionCodeVersionLog>();
		/// <summary>
		/// 设备驱动参数配置
		/// </summary>
		public ITable<LcIotDriver>            LcIotDrivers            => this.GetTable<LcIotDriver>();
		/// <summary>
		/// 设备定义
		/// </summary>
		public ITable<LcIotEquipment>         LcIotEquipment          => this.GetTable<LcIotEquipment>();
		/// <summary>
		/// 设备变量定义
		/// </summary>
		public ITable<LcIotEquipmentVariable> LcIotEquipmentVariables => this.GetTable<LcIotEquipmentVariable>();
		public ITable<LcJob>                  LcJobs                  => this.GetTable<LcJob>();
		public ITable<LcJobLog>               LcJobLogs               => this.GetTable<LcJobLog>();
		/// <summary>
		/// 消息事件定义
		/// </summary>
		public ITable<LcMessageEvent>         LcMessageEvents         => this.GetTable<LcMessageEvent>();
		/// <summary>
		/// 消息事件映射关系
		/// </summary>
		public ITable<LcMessageEventMapping>  LcMessageEventMappings  => this.GetTable<LcMessageEventMapping>();
		/// <summary>
		/// 通知通道配置
		/// </summary>
		public ITable<LcNotificationChannel>  LcNotificationChannels  => this.GetTable<LcNotificationChannel>();
		/// <summary>
		/// 在线函数管理
		/// </summary>
		public ITable<LcOnlineFunction>       LcOnlineFunctions       => this.GetTable<LcOnlineFunction>();
		/// <summary>
		/// 项目
		/// </summary>
		public ITable<LcProject>              LcProjects              => this.GetTable<LcProject>();
		/// <summary>
		/// 系统发布配置
		/// </summary>
		public ITable<LcPublish>              LcPublishes             => this.GetTable<LcPublish>();
		/// <summary>
		/// 系统角色定义
		/// </summary>
		public ITable<LcRole>                 LcRoles                 => this.GetTable<LcRole>();
		/// <summary>
		/// 服务器定义
		/// </summary>
		public ITable<LcServer>               LcServers               => this.GetTable<LcServer>();
		/// <summary>
		/// 解决方案
		/// </summary>
		public ITable<LcSolution>             LcSolutions             => this.GetTable<LcSolution>();
		/// <summary>
		/// 标签
		/// </summary>
		public ITable<LcTag>                  LcTags                  => this.GetTable<LcTag>();
		/// <summary>
		/// 标签映射
		/// </summary>
		public ITable<LcTagMapping>           LcTagMappings           => this.GetTable<LcTagMapping>();
		/// <summary>
		/// 触发器版本绑定
		/// </summary>
		public ITable<LcTriggerVersionBinding> LcTriggerVersionBindings => this.GetTable<LcTriggerVersionBinding>();
		/// <summary>
		/// 系统用户定义
		/// </summary>
		public ITable<LcUser>                 LcUsers                 => this.GetTable<LcUser>();
		/// <summary>
		/// 系统用户关联角色定义
		/// </summary>
		public ITable<LcUserRole>             LcUserRoles             => this.GetTable<LcUserRole>();
	}

	public static partial class ExtensionMethods
	{
		#region Table Extensions
		public static LcActionLibrary? Find(this ITable<LcActionLibrary> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcActionLibrary?> FindAsync(this ITable<LcActionLibrary> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcActionLibraryLog? Find(this ITable<LcActionLibraryLog> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcActionLibraryLog?> FindAsync(this ITable<LcActionLibraryLog> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcApi? Find(this ITable<LcApi> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcApi?> FindAsync(this ITable<LcApi> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcApiCluster? Find(this ITable<LcApiCluster> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcApiCluster?> FindAsync(this ITable<LcApiCluster> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcApiResponse? Find(this ITable<LcApiResponse> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcApiResponse?> FindAsync(this ITable<LcApiResponse> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcAppUser? Find(this ITable<LcAppUser> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcAppUser?> FindAsync(this ITable<LcAppUser> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcChatConversation? Find(this ITable<LcChatConversation> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcChatConversation?> FindAsync(this ITable<LcChatConversation> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcChatMessage? Find(this ITable<LcChatMessage> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcChatMessage?> FindAsync(this ITable<LcChatMessage> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcChatModelConfig? Find(this ITable<LcChatModelConfig> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcChatModelConfig?> FindAsync(this ITable<LcChatModelConfig> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcChatProfile? Find(this ITable<LcChatProfile> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcChatProfile?> FindAsync(this ITable<LcChatProfile> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcDataDict? Find(this ITable<LcDataDict> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcDataDict?> FindAsync(this ITable<LcDataDict> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcDataSource? Find(this ITable<LcDataSource> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcDataSource?> FindAsync(this ITable<LcDataSource> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcDataSourceAttr? Find(this ITable<LcDataSourceAttr> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcDataSourceAttr?> FindAsync(this ITable<LcDataSourceAttr> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcDirTree? Find(this ITable<LcDirTree> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcDirTree?> FindAsync(this ITable<LcDirTree> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFunction? Find(this ITable<LcFunction> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFunction?> FindAsync(this ITable<LcFunction> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFunctionAttr? Find(this ITable<LcFunctionAttr> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFunctionAttr?> FindAsync(this ITable<LcFunctionAttr> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcFunctionCode? Find(this ITable<LcFunctionCode> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcFunctionCode?> FindAsync(this ITable<LcFunctionCode> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcIotDriver? Find(this ITable<LcIotDriver> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcIotDriver?> FindAsync(this ITable<LcIotDriver> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcIotEquipment? Find(this ITable<LcIotEquipment> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcIotEquipment?> FindAsync(this ITable<LcIotEquipment> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcIotEquipmentVariable? Find(this ITable<LcIotEquipmentVariable> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcIotEquipmentVariable?> FindAsync(this ITable<LcIotEquipmentVariable> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcJob? Find(this ITable<LcJob> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcJob?> FindAsync(this ITable<LcJob> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcJobLog? Find(this ITable<LcJobLog> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcJobLog?> FindAsync(this ITable<LcJobLog> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcMessageEvent? Find(this ITable<LcMessageEvent> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcMessageEvent?> FindAsync(this ITable<LcMessageEvent> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcMessageEventMapping? Find(this ITable<LcMessageEventMapping> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcMessageEventMapping?> FindAsync(this ITable<LcMessageEventMapping> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcNotificationChannel? Find(this ITable<LcNotificationChannel> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcNotificationChannel?> FindAsync(this ITable<LcNotificationChannel> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcOnlineFunction? Find(this ITable<LcOnlineFunction> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcOnlineFunction?> FindAsync(this ITable<LcOnlineFunction> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcProject? Find(this ITable<LcProject> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcProject?> FindAsync(this ITable<LcProject> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcPublish? Find(this ITable<LcPublish> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcPublish?> FindAsync(this ITable<LcPublish> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcRole? Find(this ITable<LcRole> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcRole?> FindAsync(this ITable<LcRole> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcServer? Find(this ITable<LcServer> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcServer?> FindAsync(this ITable<LcServer> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcSolution? Find(this ITable<LcSolution> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcSolution?> FindAsync(this ITable<LcSolution> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcTag? Find(this ITable<LcTag> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcTag?> FindAsync(this ITable<LcTag> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcTagMapping? Find(this ITable<LcTagMapping> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcTagMapping?> FindAsync(this ITable<LcTagMapping> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcUser? Find(this ITable<LcUser> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcUser?> FindAsync(this ITable<LcUser> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}

		public static LcUserRole? Find(this ITable<LcUserRole> table, string id)
		{
			return table.FirstOrDefault(e => e.Id == id);
		}

		public static Task<LcUserRole?> FindAsync(this ITable<LcUserRole> table, string id, CancellationToken cancellationToken = default)
		{
			return table.FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
		}
		#endregion
	}
}
