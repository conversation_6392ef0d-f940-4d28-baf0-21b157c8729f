namespace GCP.Functions.Common.Models
{
    /// <summary>
    /// 保存代码结果
    /// </summary>
    public class SaveCodeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public long Version { get; set; }
        public bool HasChanges { get; set; }
    }

    /// <summary>
    /// 发布结果
    /// </summary>
    public class PublishResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// 版本比较结果
    /// </summary>
    public class VersionCompareResult
    {
        public long Version1 { get; set; }
        public long Version2 { get; set; }
        public bool HasChanges { get; set; }
        public string ConfigDiff { get; set; }
        public string Code1 { get; set; }
        public string Code2 { get; set; }
    }

    /// <summary>
    /// 函数版本日志VO
    /// </summary>
    public class FunctionVersionLogVO
    {
        public string Id { get; set; }
        public string FunctionId { get; set; }
        public long Version { get; set; }
        public long? PreviousVersion { get; set; }
        public string OperationType { get; set; }
        public string ChangeSummary { get; set; }
        public string Remark { get; set; }
        public string Creator { get; set; }
        public DateTime TimeCreate { get; set; }
        public bool IsCurrentVersion { get; set; } // 是否为当前使用版本
    }
}
