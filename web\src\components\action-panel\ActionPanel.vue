<template>
  <div v-if="visible" v-loading="loading" class="form-container">
    <div class="form-nav">
      <div class="nav-left">
        <t-button size="large" variant="text" class="nav-back" @click="onClickBack">
          <template #icon>
            <chevron-left-icon />
          </template>
        </t-button>
        <div class="nav-title">{{ flowTitle }}</div>
      </div>
      <div class="nav-center">
        <ul class="nav-menu">
          <li :class="{ 'nav-menu-item': true, active: flowTab === 0 }" @click="flowTab = 0">基础信息</li>
          <li :class="{ 'nav-menu-item': true, active: flowTab === 1 }" @click="flowTab = 1">动作配置</li>
          <li :class="{ 'nav-menu-item': true, active: flowTab === 2 }" @click="flowTab = 2">版本日志</li>
        </ul>
      </div>
      <div class="nav-right">
        <t-button theme="default" @click="onClickConfig">配 置</t-button>
        <t-button @click="onClickSave">保 存</t-button>
        <t-button theme="primary" @click="onClickPublish" :disabled="!canPublish">发 布</t-button>
      </div>
    </div>
    <div v-if="visible" class="form-content">
      <div v-show="flowTab === 0">
        <action-info></action-info>
      </div>
      <div v-show="flowTab === 1">
        <action-configure></action-configure>
      </div>
      <div v-show="flowTab === 2">
        <version-log :function-id="props.actionId"></version-log>
      </div>
    </div>
    <move-action-dialog></move-action-dialog>
    <value-dialog></value-dialog>
    <variable-batch-dialog></variable-batch-dialog>
    <config-dialog></config-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ActionPanel',
};
</script>
<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { ChevronLeftIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref, watch, watchEffect, computed } from 'vue';

import { api, Services } from '@/api/system';

import ActionConfigure from './ActionConfigure.vue';
import ActionInfo from './ActionInfo.vue';
import ConfigDialog from './ConfigDialog.vue';
import { FlowData } from './model';
import MoveActionDialog from './MoveActionDialog.vue';
import { useActionFlowStore } from './store/index';
import ValueDialog from './ValueDialog.vue';
import VariableBatchDialog from './VariableBatchDialog.vue';
import VersionLog from './VersionLog.vue';

const actionFlowStore = useActionFlowStore();

const props = defineProps<{
  visible?: boolean;
  actionId: string;
  data?: FlowData[];
}>();
const emits = defineEmits(['back', 'save', 'update:visible']);

const visible = ref(props.visible);
watch(
  () => visible.value,
  (val) => {
    emits('update:visible', val);
  },
);

watchEffect(() => {
  visible.value = props.visible;
});

const { flowTab, flowTitle, showFlowConfigDialog } = storeToRefs(actionFlowStore);

const setCustomize = (data: FlowData[]) => {
  data?.forEach((item) => {
    if (item.isCustomize === undefined || item.isCustomize === null) {
      item.isCustomize = false;
      if (item.children) {
        setCustomize(item.children);
      }
    }
  });
};

const loading = ref(false);
const fetchFlowBody = (funcId: string) => {
  if (isEmpty(funcId)) return;
  loading.value = true;
  api
    .run(Services.functionGetVersion, { id: funcId })
    .then(async (version) => {
      if (version) {
        await api.run(Services.functionCodeGetCodeByVersion, { funcId, version }).then((code) => {
          const flowInfo = code
            ? JSON.parse(code)
            : {
                id: funcId,
                version,
                data: [],
                body: [],
              };
          if (props.data && props.data.length > 0) {
            setCustomize(props.data);
            actionFlowStore.setFlowVariables(props.data);
            flowInfo.data = flowInfo.data.filter((item) => item.isCustomize);
            for (const item of props.data) {
              const index = flowInfo.data.findIndex((i) => i.id === item.id);
              if (index === -1) {
                flowInfo.data.push(item);
              }
            }
          }

          actionFlowStore.setFlowInfo(flowInfo);
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// watch(
//   () => props.actionId,
//   debounce((val) => {
//     fetchFlowBody(val);
//   }, 300),
//   { immediate: true },
// );

const onClickBack = () => {
  actionFlowStore.resetFlowInfo();
  visible.value = false;
  emits('back');
};

const onClickConfig = () => {
  showFlowConfigDialog.value = true;
};
const onClickSave = async () => {
  try {
    const result = await api.run(Services.functionCodeSaveCode, {
      funcId: props.actionId,
      codeLanguage: 'JSON',
      code: JSON.stringify(actionFlowStore.flowInfo),
      remark: '手动保存',
    });

    if (result.success) {
      // 更新本地版本号
      actionFlowStore.flowInfo.version = result.version;
      MessagePlugin.success(result.message);
      emits('save');
    } else {
      MessagePlugin.warning(result.message);
    }
  } catch (error) {
    MessagePlugin.error('保存失败：' + error.message);
  }
};

// 发布按钮是否可用
const canPublish = computed(() => {
  const version = actionFlowStore.flowInfo?.version;
  return typeof version === 'number' ? version > 0 : parseInt(String(version || '0')) > 0;
});

const onClickPublish = async () => {
  try {
    // 这里需要根据实际情况确定触发器类型和ID
    // 暂时使用示例值，实际应该从上下文获取
    const result = await api.run(Services.functionCodePublishVersion, {
      funcId: props.actionId,
      version: actionFlowStore.flowInfo.version,
      triggerType: 'ACTION', // 动作类型
      triggerId: props.actionId, // 使用动作ID作为触发器ID
      remark: '手动发布',
    });

    if (result.success) {
      MessagePlugin.success(result.message);
    } else {
      MessagePlugin.warning(result.message);
    }
  } catch (error) {
    MessagePlugin.error('发布失败：' + error.message);
  }
};

defineExpose({
  reload: fetchFlowBody,
});
</script>
<style lang="less" scoped>
@import '@/style/form.less';

.form-container {
  position: relative;
  height: 100%;

  .form-nav {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 997;
    width: 100%;
    height: 48px;
    font-size: 14px;
    // color: #fff;
    // background: #3296fa;
    color: var(--td-text-color-secondary);
    background: #fff;
    border-bottom: 1px solid var(--td-component-stroke);
    box-shadow: 0 0 20px 5px rgb(0 0 0 / 5%);
    display: flex;
    align-items: center;

    > * {
      flex: 1;
      width: 100%;
    }

    .nav-title {
      margin-left: 3px;
      font-size: 14px;
      color: var(--td-text-color-primary);
    }

    .nav-left {
      display: flex;
      align-items: center;

      .nav-back {
        height: 48px;
      }
    }

    .nav-center {
      flex: none;
      width: 600px;
      text-align: center;

      .nav-menu {
        list-style: none;
        margin: 0;

        .nav-menu-item {
          transition:
            border-color var(0.3s),
            background-color var(0.3s),
            color var(0.3s);
          display: inline-block;
          margin: 0;
          padding: 0 32px;
          height: 48px;
          line-height: 48px;
          // color: #fff;
          cursor: pointer;

          &:hover,
          &.active {
            // background-color: #40a9ff;
            color: var(--td-brand-color);
            background: var(--td-brand-color-light);
          }

          &.active {
            // background-color: #40a9ff;
            border-bottom: 2px solid var(--td-brand-color);
          }
        }
      }
    }

    .nav-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      text-align: right;
      padding-right: 24px;
    }
  }

  .form-content {
    position: absolute;
    inset: 48px 0 0;
    z-index: 1;
    overflow: hidden auto;
    // padding-bottom: 30px;
    > div {
      height: 100%;
    }
  }
}
</style>
