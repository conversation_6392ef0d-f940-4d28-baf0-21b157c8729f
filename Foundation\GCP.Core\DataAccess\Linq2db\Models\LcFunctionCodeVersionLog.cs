// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 函数代码版本日志
	/// </summary>
	[Table("lc_function_code_version_log")]
	public class LcFunctionCodeVersionLog : IBaseEntity
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"               , CanBeNull = false, IsPrimaryKey = true)] public string    Id              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:创建时间
		/// </summary>
		[Column("TIME_CREATE"                                               )] public DateTime  TimeCreate      { get; set; } // datetime
		/// <summary>
		/// Description:创建人
		/// </summary>
		[Column("CREATOR"          , CanBeNull = false                      )] public string    Creator         { get; set; } = null!; // varchar(80)
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"      , CanBeNull = false                      )] public string    SolutionId      { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"       , CanBeNull = false                      )] public string    ProjectId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:函数 ID
		/// </summary>
		[Column("FUNCTION_ID"      , CanBeNull = false                      )] public string    FunctionId      { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:版本号
		/// </summary>
		[Column("VERSION"                                                   )] public long      Version         { get; set; } // bigint
		/// <summary>
		/// Description:上一个版本号
		/// </summary>
		[Column("PREVIOUS_VERSION"                                          )] public long?     PreviousVersion { get; set; } // bigint
		/// <summary>
		/// Description:操作类型 CREATE/UPDATE/ROLLBACK/PUBLISH
		/// </summary>
		[Column("OPERATION_TYPE"   , CanBeNull = false                      )] public string    OperationType   { get; set; } = null!; // varchar(20)
		/// <summary>
		/// Description:变更摘要
		/// </summary>
		[Column("CHANGE_SUMMARY"                                            )] public string?   ChangeSummary   { get; set; } // varchar(500)
		/// <summary>
		/// Description:备注
		/// </summary>
		[Column("REMARK"                                                    )] public string?   Remark          { get; set; } // varchar(1000)
		/// <summary>
		/// Description:配置差异JSON
		/// </summary>
		[Column("CONFIG_DIFF"                                               )] public string?   ConfigDiff      { get; set; } // longtext
		/// <summary>
		/// Description:是否已发布
		/// </summary>
		[Column("IS_PUBLISHED"                                              )] public bool      IsPublished     { get; set; } // tinyint(1)
		/// <summary>
		/// Description:发布时间
		/// </summary>
		[Column("PUBLISH_TIME"                                              )] public DateTime? PublishTime     { get; set; } // datetime
		/// <summary>
		/// Description:发布人
		/// </summary>
		[Column("PUBLISH_USER"                                              )] public string?   PublishUser     { get; set; } // varchar(80)
	}
}
