using FluentMigrator;

namespace GCP.DataAccess.Migrations.FoundationMigration.Job
{
    [Migration(20250717121900, "添加版本日志管理表")]
    public class AddVersionLogTables : Migration
    {
        public override void Up()
        {
            // 函数代码版本日志表
            Create.Table("LC_FUNCTION_CODE_VERSION_LOG").WithDescription("函数代码版本日志")
               .WithColumn("ID").AsAnsiString(36).PrimaryKey().WithColumnDescription("数据行 ID 号")
               .WithColumn("TIME_CREATE").AsDateTime().WithDefault(SystemMethods.CurrentDateTime).WithColumnDescription("创建时间")
               .WithColumn("CREATOR").AsAnsiString(80).WithColumnDescription("创建人")
               .WithColumn("TIME_MODIFIED").AsDateTime().Nullable().WithColumnDescription("更新时间")
               .WithColumn("MODIFIER").AsAnsiString(80).Nullable().WithColumnDescription("更新数据行的用户")
               .WithColumn("STATE").AsInt16().WithDefaultValue(1).WithColumnDescription("可用状态")
               .WithColumn("SOLUTION_ID").AsAnsiString(36).WithColumnDescription("解决方案 ID")
               .WithColumn("PROJECT_ID").AsAnsiString(36).WithColumnDescription("项目 ID")

               .WithColumn("FUNCTION_ID").AsAnsiString(36).WithColumnDescription("函数 ID")
               .WithColumn("VERSION").AsInt64().WithColumnDescription("版本号")
               .WithColumn("PREVIOUS_VERSION").AsInt64().Nullable().WithColumnDescription("上一个版本号")
               .WithColumn("OPERATION_TYPE").AsAnsiString(20).WithColumnDescription("操作类型 CREATE/UPDATE/ROLLBACK/PUBLISH")
               .WithColumn("CHANGE_SUMMARY").AsAnsiString(500).Nullable().WithColumnDescription("变更摘要")
               .WithColumn("REMARK").AsAnsiString(1000).Nullable().WithColumnDescription("备注")
               ;

            Create.Index("FUNC_VERSION_LOG_IDX")
                .OnTable("LC_FUNCTION_CODE_VERSION_LOG")
                .OnColumn("FUNCTION_ID").Ascending()
                .OnColumn("VERSION").Descending()
                .OnColumn("SOLUTION_ID").Ascending()
                .OnColumn("PROJECT_ID").Ascending();


        }

        public override void Down()
        {
            Delete.Table("LC_FUNCTION_CODE_VERSION_LOG");
        }
    }
}
