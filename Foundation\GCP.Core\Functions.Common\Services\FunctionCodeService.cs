using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool;
using GCP.Functions.Common.Models;
using LinqToDB;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    [Function("functionCode", "函数代码服务")]
    class FunctionCodeService : BaseService
    {
        [Function("getAll", "获取函数代码清单")]
        public List<FunctionCodeInfoVO> GetAll(string funcId = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        join b in db.LcFunctionCodes on a.Id equals b.FunctionId
                        where a.State == 1 && (string.IsNullOrEmpty(funcId) || a.Id == funcId)
                        select new FunctionCodeInfoVO
                        {
                            Id = a.Id,
                            SolutionId = a.SolutionId,
                            ProjectId = a.ProjectId,
                            Description = a.FunctionName,
                            UseVersion = a.UseVersion,
                            FunctionType = a.FunctionType,
                            CodeLanguage = b.CodeLanguage,
                            Code = b.Code
                        }).ToList();
            return data;
        }

        [Function("getCodeByVersion", "获取指定版本的函数代码")]
        public string GetCodeByVersion(string funcId, long? version = null)
        {
            var item = GetByVersion(funcId, version);

            //if(item.CodeLanguage == "JSON")
            //{
            //    try
            //    {
            //        var flow = JsonHelper.Deserialize<FunctionFlow>(item?.Code);
            //        return JsonHelper.Serialize(flow);
            //    }
            //    catch (Exception ex)
            //    {
            //        throw new CustomException("函数代码转换格式错误，请检查", ex);
            //    }
            //}

            return item?.Code;
        }

        private LcFunctionCode GetByVersion(string funcId, long? version = null)
        {
            using var db = this.GetDb();
            var list = (from a in db.LcFunctionCodes
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        a.FunctionId == funcId
                        select a).ToList();

            if (list.Count == 0)
            {
                return null;
            }

            LcFunctionCode item = null;
            if (version.HasValue)
            {
                item = list.FirstOrDefault(a => a.Version == version);
                if (item == null)
                {
                    return null;
                }
            }
            else
            {
                item = list.OrderByDescending(a => a.Version).First();
            }

            return item;
        }

        [Function("saveCode", "保存函数代码")]
        public SaveCodeResult SaveCode(string funcId, string code, string codeLanguage, long? version = null, string remark = null)
        {
            using var db = this.GetDb();

            // 获取当前最新版本
            var currentVersion = GetLatestVersion(funcId);
            var newVersion = version ?? (currentVersion + 1);

            // 检查配置是否有变化
            var hasChanges = true;
            var configDiff = "";
            var previousVersion = currentVersion;

            if (currentVersion > 0)
            {
                var previousCode = GetCodeByVersion(funcId, currentVersion);
                hasChanges = !string.Equals(previousCode, code, StringComparison.Ordinal);

                if (hasChanges)
                {
                    configDiff = GenerateConfigDiff(previousCode, code);
                }
            }

            // 如果没有变化且不是强制保存，返回结果
            if (!hasChanges && version == null)
            {
                return new SaveCodeResult
                {
                    Success = false,
                    Message = "配置未发生变化，无需保存",
                    Version = currentVersion,
                    HasChanges = false
                };
            }

            db.BeginTransaction();
            try
            {
                // 保存代码
                var item = GetByVersion(funcId, newVersion);
                if (item == null)
                {
                    item = new()
                    {
                        SolutionId = this.SolutionId,
                        ProjectId = this.ProjectId,
                        FunctionId = funcId,
                        Code = code,
                        CodeLanguage = codeLanguage,
                        Version = newVersion,
                    };
                    this.InsertData(item, db);
                }
                else
                {
                    item.Code = code;
                    this.UpdateData(item, db);
                }

                // 记录版本日志
                var versionLog = new LcFunctionCodeVersionLog
                {
                    SolutionId = this.SolutionId,
                    ProjectId = this.ProjectId,
                    FunctionId = funcId,
                    Version = newVersion,
                    PreviousVersion = previousVersion > 0 ? previousVersion : null,
                    OperationType = currentVersion == 0 ? "CREATE" : "UPDATE",
                    ChangeSummary = hasChanges ? "配置已更新" : "强制保存",
                    Remark = remark,
                    ConfigDiff = configDiff,
                    IsPublished = false
                };
                this.InsertData(versionLog, db);

                // 更新函数的使用版本
                var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
                if (function != null)
                {
                    function.UseVersion = newVersion;
                    this.UpdateData(function, db);
                }

                db.CommitTransaction();

                this.Context.Current.Target = item.FunctionId;
                ResiliencePipelineManager.TryRemoveByPrefix(FlowExecutor.FlowPipelineKeyPrefix + funcId);

                return new SaveCodeResult
                {
                    Success = true,
                    Message = "保存成功",
                    Version = newVersion,
                    HasChanges = hasChanges
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("getLatestVersion", "获取最新版本号")]
        public long GetLatestVersion(string funcId)
        {
            using var db = this.GetDb();
            var maxVersion = db.LcFunctionCodes
                .Where(a => a.State == 1 &&
                           a.SolutionId == this.SolutionId &&
                           a.ProjectId == this.ProjectId &&
                           a.FunctionId == funcId)
                .Max(a => (long?)a.Version);

            return maxVersion ?? 0;
        }

        [Function("getVersionLogs", "获取版本日志")]
        public List<FunctionVersionLogVO> GetVersionLogs(string funcId, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var query = from log in db.LcFunctionCodeVersionLogs
                       where log.SolutionId == this.SolutionId &&
                             log.ProjectId == this.ProjectId &&
                             log.FunctionId == funcId
                       orderby log.Version descending
                       select new FunctionVersionLogVO
                       {
                           Id = log.Id,
                           FunctionId = log.FunctionId,
                           Version = log.Version,
                           PreviousVersion = log.PreviousVersion,
                           OperationType = log.OperationType,
                           ChangeSummary = log.ChangeSummary,
                           Remark = log.Remark,
                           ConfigDiff = log.ConfigDiff,
                           IsPublished = log.IsPublished,
                           PublishTime = log.PublishTime,
                           PublishUser = log.PublishUser,
                           Creator = log.Creator,
                           TimeCreate = log.TimeCreate
                       };

            return query.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
        }

        [Function("compareVersions", "比较版本差异")]
        public VersionCompareResult CompareVersions(string funcId, long version1, long version2)
        {
            var code1 = GetCodeByVersion(funcId, version1);
            var code2 = GetCodeByVersion(funcId, version2);

            if (code1 == null || code2 == null)
            {
                throw new CustomException("指定版本不存在");
            }

            var diff = GenerateConfigDiff(code1, code2);
            var hasChanges = !string.Equals(code1, code2, StringComparison.Ordinal);

            return new VersionCompareResult
            {
                Version1 = version1,
                Version2 = version2,
                HasChanges = hasChanges,
                ConfigDiff = diff,
                Code1 = code1,
                Code2 = code2
            };
        }

        [Function("rollbackToVersion", "回滚到指定版本")]
        public SaveCodeResult RollbackToVersion(string funcId, long targetVersion, string remark = null)
        {
            var targetCode = GetCodeByVersion(funcId, targetVersion);
            if (targetCode == null)
            {
                throw new CustomException("目标版本不存在");
            }

            var currentVersion = GetLatestVersion(funcId);
            var newVersion = currentVersion + 1;

            using var db = this.GetDb();
            db.BeginTransaction();
            try
            {
                // 创建新版本（回滚版本）
                var item = new LcFunctionCode
                {
                    SolutionId = this.SolutionId,
                    ProjectId = this.ProjectId,
                    FunctionId = funcId,
                    Code = targetCode,
                    CodeLanguage = "JSON", // 假设都是JSON格式
                    Version = newVersion,
                };
                this.InsertData(item, db);

                // 记录版本日志
                var versionLog = new LcFunctionCodeVersionLog
                {
                    SolutionId = this.SolutionId,
                    ProjectId = this.ProjectId,
                    FunctionId = funcId,
                    Version = newVersion,
                    PreviousVersion = currentVersion,
                    OperationType = "ROLLBACK",
                    ChangeSummary = $"回滚到版本 {targetVersion}",
                    Remark = remark,
                    ConfigDiff = GenerateConfigDiff(GetCodeByVersion(funcId, currentVersion), targetCode),
                    IsPublished = false
                };
                this.InsertData(versionLog, db);

                // 更新函数的使用版本
                var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
                if (function != null)
                {
                    function.UseVersion = newVersion;
                    this.UpdateData(function, db);
                }

                db.CommitTransaction();

                this.Context.Current.Target = item.FunctionId;
                ResiliencePipelineManager.TryRemoveByPrefix(FlowExecutor.FlowPipelineKeyPrefix + funcId);

                return new SaveCodeResult
                {
                    Success = true,
                    Message = $"已回滚到版本 {targetVersion}",
                    Version = newVersion,
                    HasChanges = true
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("publishVersion", "发布版本到触发器")]
        public PublishResult PublishVersion(string funcId, long version, string triggerType, string triggerId, string remark = null)
        {
            using var db = this.GetDb();

            // 检查版本是否存在
            var codeVersion = GetByVersion(funcId, version);
            if (codeVersion == null)
            {
                throw new CustomException("指定版本不存在");
            }

            // 检查是否有成功的执行日志（这里需要根据实际的日志表结构来实现）
            var hasSuccessLog = CheckVersionHasSuccessLog(funcId, version);
            if (!hasSuccessLog)
            {
                return new PublishResult
                {
                    Success = false,
                    Message = "该版本没有成功执行记录，无法发布"
                };
            }

            db.BeginTransaction();
            try
            {
                // 更新或创建触发器版本绑定
                var existingBinding = db.LcTriggerVersionBindings
                    .FirstOrDefault(b => b.TriggerType == triggerType &&
                                        b.TriggerId == triggerId &&
                                        b.FunctionId == funcId &&
                                        b.State == 1);

                if (existingBinding != null)
                {
                    existingBinding.BoundVersion = version;
                    existingBinding.BindTime = DateTime.Now;
                    existingBinding.BindUser = this.Context.Current.User;
                    existingBinding.Remark = remark;
                    this.UpdateData(existingBinding, db);
                }
                else
                {
                    var newBinding = new LcTriggerVersionBinding
                    {
                        SolutionId = this.SolutionId,
                        ProjectId = this.ProjectId,
                        TriggerType = triggerType,
                        TriggerId = triggerId,
                        FunctionId = funcId,
                        BoundVersion = version,
                        BindTime = DateTime.Now,
                        BindUser = this.Context.Current.User,
                        Remark = remark
                    };
                    this.InsertData(newBinding, db);
                }

                // 更新版本日志的发布状态
                var versionLog = db.LcFunctionCodeVersionLogs
                    .FirstOrDefault(l => l.FunctionId == funcId && l.Version == version);
                if (versionLog != null)
                {
                    versionLog.IsPublished = true;
                    versionLog.PublishTime = DateTime.Now;
                    versionLog.PublishUser = this.Context.Current.User;
                    this.UpdateData(versionLog, db);
                }

                db.CommitTransaction();

                return new PublishResult
                {
                    Success = true,
                    Message = "发布成功"
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("getTriggerBindings", "获取触发器版本绑定")]
        public List<TriggerVersionBindingVO> GetTriggerBindings(string funcId)
        {
            using var db = this.GetDb();
            var query = from binding in db.LcTriggerVersionBindings
                       where binding.SolutionId == this.SolutionId &&
                             binding.ProjectId == this.ProjectId &&
                             binding.FunctionId == funcId &&
                             binding.State == 1
                       select new TriggerVersionBindingVO
                       {
                           Id = binding.Id,
                           TriggerType = binding.TriggerType,
                           TriggerId = binding.TriggerId,
                           FunctionId = binding.FunctionId,
                           BoundVersion = binding.BoundVersion,
                           BindTime = binding.BindTime,
                           BindUser = binding.BindUser,
                           Remark = binding.Remark
                       };

            return query.ToList();
        }

        private string GenerateConfigDiff(string oldCode, string newCode)
        {
            // 简单的差异生成，实际项目中可以使用更复杂的diff算法
            if (string.Equals(oldCode, newCode, StringComparison.Ordinal))
            {
                return "无变化";
            }

            try
            {
                var oldJson = JsonSerializer.Deserialize<object>(oldCode ?? "{}");
                var newJson = JsonSerializer.Deserialize<object>(newCode ?? "{}");

                // 这里可以实现更详细的JSON差异比较
                return "配置已更新";
            }
            catch
            {
                return "配置格式发生变化";
            }
        }

        private bool CheckVersionHasSuccessLog(string funcId, long version)
        {
            // 这里需要根据实际的执行日志表来实现
            // 暂时返回true，实际实现时需要查询相关的日志表
            using var db = this.GetDb();

            // 示例：检查是否有成功的执行记录
            // 这里需要根据实际的日志表结构来实现
            return true; // 临时返回true
        }
