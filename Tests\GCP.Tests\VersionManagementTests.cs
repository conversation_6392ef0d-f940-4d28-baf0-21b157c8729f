using GCP.Functions.Common.Services;
using GCP.Functions.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace GCP.Tests
{
    public class VersionManagementTests : BaseTest
    {
        private readonly FunctionCodeService _functionCodeService;

        public VersionManagementTests()
        {
            _functionCodeService = ServiceProvider.GetRequiredService<FunctionCodeService>();
        }

        [Fact]
        public void SaveCode_WithNewVersion_ShouldCreateNewVersion()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";
            var remark = "测试创建新版本";

            // Act
            var result = _functionCodeService.SaveCode(funcId, code, "JSON", null, remark);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.HasChanges);
            Assert.True(result.Version > 0);
        }

        [Fact]
        public void SaveCode_WithSameContent_ShouldNotCreateNewVersion()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";

            // 先保存一次
            _functionCodeService.SaveCode(funcId, code, "JSON", null, "初始版本");

            // Act - 再次保存相同内容
            var result = _functionCodeService.SaveCode(funcId, code, "JSON", null, "重复保存");

            // Assert
            Assert.False(result.Success);
            Assert.False(result.HasChanges);
            Assert.Contains("配置未发生变化", result.Message);
        }

        [Fact]
        public void GetVersionLogs_ShouldReturnVersionHistory()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code1 = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";
            var code2 = @"{""id"":""test"",""version"":2,""data"":[{""id"":""new""}],""body"":[]}";

            // 创建两个版本
            _functionCodeService.SaveCode(funcId, code1, "JSON", null, "版本1");
            _functionCodeService.SaveCode(funcId, code2, "JSON", null, "版本2");

            // Act
            var logs = _functionCodeService.GetVersionLogs(funcId, 1, 10);

            // Assert
            Assert.NotEmpty(logs);
            Assert.True(logs.Count >= 2);
            Assert.Contains(logs, l => l.Remark == "版本1");
            Assert.Contains(logs, l => l.Remark == "版本2");
        }

        [Fact]
        public void CompareVersions_ShouldReturnDifferences()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code1 = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";
            var code2 = @"{""id"":""test"",""version"":2,""data"":[{""id"":""new""}],""body"":[]}";

            // 创建两个版本
            var result1 = _functionCodeService.SaveCode(funcId, code1, "JSON", null, "版本1");
            var result2 = _functionCodeService.SaveCode(funcId, code2, "JSON", null, "版本2");

            // Act
            var compareResult = _functionCodeService.CompareVersions(funcId, result1.Version, result2.Version);

            // Assert
            Assert.NotNull(compareResult);
            Assert.True(compareResult.HasChanges);
            Assert.Equal(result1.Version, compareResult.Version1);
            Assert.Equal(result2.Version, compareResult.Version2);
        }

        [Fact]
        public void RollbackToVersion_ShouldCreateNewVersionWithOldContent()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code1 = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";
            var code2 = @"{""id"":""test"",""version"":2,""data"":[{""id"":""new""}],""body"":[]}";

            // 创建两个版本
            var result1 = _functionCodeService.SaveCode(funcId, code1, "JSON", null, "版本1");
            var result2 = _functionCodeService.SaveCode(funcId, code2, "JSON", null, "版本2");

            // Act - 回滚到版本1
            var rollbackResult = _functionCodeService.RollbackToVersion(funcId, result1.Version, "回滚测试");

            // Assert
            Assert.True(rollbackResult.Success);
            Assert.True(rollbackResult.HasChanges);
            Assert.True(rollbackResult.Version > result2.Version);

            // 验证回滚后的内容
            var rolledBackCode = _functionCodeService.GetCodeByVersion(funcId, rollbackResult.Version);
            Assert.Equal(code1, rolledBackCode);
        }

        [Fact]
        public void PublishVersion_WithValidVersion_ShouldSucceed()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";

            // 创建版本
            var saveResult = _functionCodeService.SaveCode(funcId, code, "JSON", null, "测试版本");

            // Act
            var publishResult = _functionCodeService.PublishVersion(
                funcId,
                saveResult.Version,
                "测试发布"
            );

            // Assert
            Assert.True(publishResult.Success);
        }

        [Fact]
        public void GetCurrentUseVersion_ShouldReturnCurrentVersion()
        {
            // Arrange
            var funcId = "test-function-" + Guid.NewGuid().ToString();
            var code = @"{""id"":""test"",""version"":1,""data"":[],""body"":[]}";

            // 创建版本并发布
            var saveResult = _functionCodeService.SaveCode(funcId, code, "JSON", null, "测试版本");
            _functionCodeService.PublishVersion(funcId, saveResult.Version, "测试发布");

            // Act
            var currentVersion = _functionCodeService.GetCurrentUseVersion(funcId);

            // Assert
            Assert.Equal(saveResult.Version, currentVersion);
        }
    }
}
